#!/bin/bash

# MCP服务器修复脚本
# 解决Node.js版本兼容性问题和MCP服务器连接失败问题

echo "🔧 开始修复MCP服务器连接问题..."

# 1. 检查当前Node.js版本
echo "📋 当前Node.js版本信息："
node --version
npm --version

# 2. 创建Claude Desktop配置目录（如果不存在）
CLAUDE_CONFIG_DIR="$HOME/.config/Claude"
if [ ! -d "$CLAUDE_CONFIG_DIR" ]; then
    echo "📁 创建Claude配置目录: $CLAUDE_CONFIG_DIR"
    mkdir -p "$CLAUDE_CONFIG_DIR"
fi

# 3. 备份现有配置（如果存在）
CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"
if [ -f "$CONFIG_FILE" ]; then
    echo "💾 备份现有配置文件"
    cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 4. 创建基础配置文件（只包含工作的寸止MCP）
echo "📝 创建基础MCP配置文件..."
cat > "$CONFIG_FILE" << 'EOF'
{
  "mcpServers": {
    "寸止": {
      "command": "寸止"
    }
  }
}
EOF

echo "✅ 基础配置已创建，寸止MCP应该可以正常工作"

# 5. 尝试安装兼容的Node.js版本（使用nvm）
echo "🔄 检查是否需要安装兼容的Node.js版本..."

# 检查是否有nvm
if command -v nvm &> /dev/null; then
    echo "📦 检测到nvm，尝试安装Node.js LTS版本..."
    
    # 安装并使用LTS版本
    nvm install --lts
    nvm use --lts
    
    echo "🔄 使用LTS版本重新测试MCP服务器..."
    
    # 重新创建包含所有MCP服务器的配置
    cat > "$CONFIG_FILE" << 'EOF'
{
  "mcpServers": {
    "寸止": {
      "command": "寸止"
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    },
    "playwright": {
      "command": "npx",
      "args": ["-y", "@playwright/mcp"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "sequential-thinking-mcp"]
    }
  }
}
EOF
    
    echo "✅ 完整配置已创建"
else
    echo "⚠️  未检测到nvm，建议手动安装Node.js LTS版本"
    echo "   当前使用的nightly版本可能导致兼容性问题"
fi

# 6. 验证配置文件格式
echo "🔍 验证配置文件格式..."
if python3 -c "import json; json.load(open('$CONFIG_FILE'))" 2>/dev/null; then
    echo "✅ 配置文件格式正确"
else
    echo "❌ 配置文件格式错误，请检查"
fi

# 7. 显示配置文件内容
echo "📄 当前配置文件内容："
cat "$CONFIG_FILE"

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重启Claude Desktop应用"
echo "2. 检查MCP服务器连接状态"
echo "3. 如果仍有问题，考虑安装Node.js LTS版本替换nightly版本"
echo ""
echo "💡 提示："
echo "- 寸止MCP应该可以正常工作"
echo "- 其他MCP服务器可能需要兼容的Node.js版本"
echo "- 建议使用Node.js 18.x 或 20.x LTS版本"
