#!/bin/bash

# MCP服务器测试脚本
echo "🧪 测试MCP服务器连接状态..."

# 测试寸止MCP
echo "1️⃣ 测试寸止MCP..."
if command -v 寸止 &> /dev/null; then
    echo "✅ 寸止MCP已安装"
    timeout 3s 寸止 &>/dev/null && echo "✅ 寸止MCP可以启动" || echo "⚠️ 寸止MCP启动测试超时（正常现象）"
else
    echo "❌ 寸止MCP未安装"
fi

# 测试Context7 MCP
echo "2️⃣ 测试Context7 MCP..."
timeout 5s npx -y @upstash/context7-mcp &>/dev/null && echo "✅ Context7 MCP可用" || echo "⚠️ Context7 MCP测试超时（正常现象）"

# 测试Playwright MCP
echo "3️⃣ 测试Playwright MCP..."
timeout 5s npx -y @playwright/mcp &>/dev/null && echo "✅ Playwright MCP可用" || echo "⚠️ Playwright MCP测试超时（正常现象）"

# 测试Sequential Thinking MCP
echo "4️⃣ 测试Sequential Thinking MCP..."
timeout 5s npx -y sequential-thinking-mcp &>/dev/null && echo "✅ Sequential Thinking MCP可用" || echo "⚠️ Sequential Thinking MCP测试超时（正常现象）"

echo ""
echo "📋 配置文件状态："
if [ -f ~/.config/Claude/claude_desktop_config.json ]; then
    echo "✅ Claude Desktop配置文件存在"
    echo "📄 配置内容："
    cat ~/.config/Claude/claude_desktop_config.json
else
    echo "❌ Claude Desktop配置文件不存在"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重启Claude Desktop应用"
echo "2. 检查MCP服务器连接状态"
echo "3. 所有服务器应该可以正常连接"
echo ""
echo "💡 说明："
echo "- 超时是正常现象，因为MCP服务器会持续运行等待连接"
echo "- 重要的是包能够正确下载和启动"
