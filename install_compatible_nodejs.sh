#!/bin/bash

# Node.js兼容版本安装脚本
# 解决nightly版本兼容性问题

echo "🔧 安装兼容的Node.js版本..."

# 1. 检查当前版本
echo "📋 当前Node.js版本："
node --version

# 2. 安装nvm（如果未安装）
if ! command -v nvm &> /dev/null; then
    echo "📦 安装nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    
    # 加载nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
fi

# 3. 安装Node.js LTS版本
echo "📦 安装Node.js LTS版本..."
nvm install --lts
nvm use --lts

# 4. 验证安装
echo "✅ 新的Node.js版本："
node --version
npm --version

# 5. 测试MCP服务器
echo "🧪 测试MCP服务器..."

echo "测试Context7 MCP..."
timeout 5s npx -y @upstash/context7-mcp --version 2>/dev/null && echo "✅ Context7 MCP可用" || echo "❌ Context7 MCP不可用"

echo "测试Playwright MCP..."
timeout 5s npx -y @playwright/mcp --version 2>/dev/null && echo "✅ Playwright MCP可用" || echo "❌ Playwright MCP不可用"

# 6. 更新Claude配置
CLAUDE_CONFIG_DIR="$HOME/.config/Claude"
CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"

echo "📝 更新Claude Desktop配置..."
cat > "$CONFIG_FILE" << 'EOF'
{
  "mcpServers": {
    "寸止": {
      "command": "寸止"
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    },
    "playwright": {
      "command": "npx",
      "args": ["-y", "@playwright/mcp"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "sequential-thinking-mcp"]
    }
  }
}
EOF

echo "✅ 配置已更新"

# 7. 添加环境变量到shell配置
echo "📝 更新shell配置..."
if [ -f "$HOME/.bashrc" ]; then
    if ! grep -q "NVM_DIR" "$HOME/.bashrc"; then
        echo "" >> "$HOME/.bashrc"
        echo "# NVM配置" >> "$HOME/.bashrc"
        echo 'export NVM_DIR="$HOME/.nvm"' >> "$HOME/.bashrc"
        echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> "$HOME/.bashrc"
        echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> "$HOME/.bashrc"
        echo "✅ 已更新.bashrc"
    fi
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重新启动终端或运行: source ~/.bashrc"
echo "2. 重启Claude Desktop应用"
echo "3. 检查MCP服务器连接状态"
echo ""
echo "💡 使用说明："
echo "- 使用 'nvm use --lts' 切换到LTS版本"
echo "- 使用 'nvm list' 查看已安装的版本"
echo "- 使用 'nvm use system' 切换回系统版本"
